"use client";

import React from "react";
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Pagination,
  Stack,
} from "@mui/material";
import { renderers } from "../renderers";
import FiltersButton from "@/_core/ui/components/button/filtersButton";
import { ColumnConfig } from "../readCollectionRenderer";
import { DataRow } from "@/_core/utils/crud";

type Props = {
  data: DataRow[];
  columnsConfig?: ColumnConfig[];
  isLoading?: boolean;
  isError?: boolean;
};

export default function TabVariant({ data, columnsConfig, isLoading, isError }: Props) {

  const columns: ColumnConfig[] = columnsConfig
    ? columnsConfig
    : Object.keys(data[0])
        .filter((col) => col !== "__actions")
        .map((key) => ({ key, renderer: renderers.default }));

  // Para saber si una columna es oculta (renderer === hidden)
  const isVisible = (col: ColumnConfig) => col.renderer !== renderers.hidden;

  return (
    <Box bgcolor={"third.light"} className="px-4 py-2">
      <Stack direction="column" spacing={2}>
        <FiltersButton onEdit={() => {}} />
        <TableContainer>
          <Table sx={{ border: "2px solid #F2F4F8" }}>
            <TableHead sx={{ bgcolor: "third.dark" }}>
              <TableRow>
                {columns.filter(isVisible).map(({ key, label }) => (
                  <TableCell key={key} sx={{ color: "third.contrastText" }}>
                    <Typography
                      className="font-bold"
                      sx={{
                        typography: {
                          xs: "body3",
                          md: "body3xl",
                          lg: "body2xl",
                        },
                      }}
                    >
                      {label ?? key}
                    </Typography>
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {data.length === 0 && !isLoading && !isError && (
                <TableRow>
                  <TableCell colSpan={columns.filter(isVisible).length}>
                    <Typography
                      className="text-center"
                      sx={{
                        typography: {
                          xs: "body3",
                          md: "body3xl",
                          lg: "body2xl",
                        },
                      }}
                      color="third.contrastText"
                    >
                      No hay datos para mostrar
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
              {isLoading && (
                <TableRow>
                  <TableCell colSpan={columns.filter(isVisible).length}>
                    <Typography
                      className="text-center"
                      sx={{
                        typography: {
                          xs: "body3",
                          md: "body3xl",
                          lg: "body2xl",
                        },
                      }}
                      color="third.contrastText"
                    >
                      Cargando...
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
              {isError && (
                <TableRow>
                  <TableCell colSpan={columns.filter(isVisible).length}>
                    <Typography
                      className="text-center"
                      sx={{
                        typography: {
                          xs: "body3",
                          md: "body3xl",
                          lg: "body2xl",
                        },
                      }}
                      color="third.contrastText"
                    >
                      Error al cargar los datos
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
              {data.map((row, index) => (
                <TableRow
                  key={index}
                  sx={{ borderBottom: "2px solid #F2F4F8" }}
                >
                  {columns
                    .filter(isVisible)
                    .map(({ key, renderer, options }) => (
                      <TableCell key={key}>
                        {renderer
                          ? renderer(row[key], row, key, options)
                          : String(row[key] ?? "")}
                      </TableCell>
                    ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <Pagination
          count={10}
          color="primary"
          className="flex justify-end"
        ></Pagination>
      </Stack>
    </Box>
  );
}
