"use client";

import { ColumnRenderer, DataRow } from "@/_core/utils/crud";
import PageVariant from "./table/pageVariant";
import TabVariant from "./table/tabVariant";
import { renderers } from "./renderers";

import { useSearchParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";

import { runFormAction } from "@/_lib/data/model/action/actionFactory";
import { EntityKeys } from "@/_lib/utils/entities";

export type ColumnConfigWithStringRenderer = {
  key: string;
  label?: string;
  renderer?: string;
  options?: Record<string, unknown>;
};

export type ColumnConfig = {
  key: string;
  label?: string;
  renderer?: ColumnRenderer;
  options?: Record<string, unknown>;
};

// Type guard to check if columnsConfig is ColumnConfigWithStringRenderer[]
function isStringRendererConfig(
  config: ColumnConfig[] | ColumnConfigWithStringRenderer[] | undefined
): config is ColumnConfigWithStringRenderer[] {
  return (
    Array.isArray(config) &&
    config.length > 0 &&
    typeof config[0].renderer === "string"
  );
}

// Convert string renderer identifiers to actual renderer functions
function convertStringRenderers(
  columnsConfig: ColumnConfigWithStringRenderer[]
): ColumnConfig[] {
  return columnsConfig.map((col) => ({
    ...col,
    renderer: col.renderer ? renderers[col.renderer] : undefined,
  }));
}

type Props = {
  entityName: EntityKeys;
  actionName: string;
  columnsConfig?: ColumnConfig[] | ColumnConfigWithStringRenderer[];
  variant?: "tab" | "page";
  reduceAction?: (row: DataRow) => Promise<DataRow>;
};

export default function ReadCollectionRenderer({
  entityName,
  actionName,
  columnsConfig,
  variant = "page",
  reduceAction,
}: Props) {
  const searchParams = useSearchParams();
  const query = Object.fromEntries(searchParams.entries());
  const { page } = query;

  // Convert string renderers to actual renderer functions if needed
  const convertedColumnsConfig = isStringRendererConfig(columnsConfig)
    ? convertStringRenderers(columnsConfig)
    : columnsConfig;

  const { data, isLoading, error } = useQuery({
    queryKey: [entityName, actionName, page],
    queryFn: async () => {
      const result = await runFormAction(entityName, actionName, {
        query: {
          ...query,
          page: page ? Number(page) : 1,
          limit: 6,
        },
      });
      const { data, meta } = result as {
        data: object[];
        meta: {
          itemsPerPage: number;
          totalItems: number;
          currentPage: number;
          totalPages: number;
        };
      };
      const reducedData = await Promise.all(
        data.map(async (row) => {
          const newRow = !!reduceAction
            ? await reduceAction(row as DataRow)
            : (row as DataRow);
          return newRow;
        })
      );
      return { items: reducedData, meta };
    },
  });

  const { items, meta } = data || {
    items: [],
    meta: { itemsPerPage: 0, totalItems: 0, currentPage: 0, totalPages: 0 },
  };

  return (
    <>
      {variant === "tab" ? (
        <TabVariant
          data={items}
          columnsConfig={convertedColumnsConfig as ColumnConfig[]}
          isLoading={isLoading}
          isError={!!error}
        />
      ) : (
        <PageVariant
          data={items}
          columnsConfig={convertedColumnsConfig as ColumnConfig[]}
          isLoading={isLoading}
          isError={!!error}
        />
      )}
    </>
  );
}
