"use client";

import { ClientFormWrapper } from "@/_core/ui/components/forms/clientFormWrapper";
import { FieldMeta } from "@/_core/ui/components/forms/types";
import { InferSchema, EntityKeys, ActionKeys } from "@/_lib/data/model/schema";
import { FormValidateAndSubmitButton } from "@/_core/ui/components/forms/formValidateAndSubmitButton";

import { Step } from "@/_core/ui/components/forms/types";

import { Box, Typography, Stack } from "@mui/material";

type CreateFormClientProps<E extends EntityKeys, A extends ActionKeys<E>> = {
  entityName: E;
  actionName: A;
  meta: Record<keyof InferSchema<E, A>, FieldMeta>;
  steps?: Step<E, A>[];
};

export function CreateFormClient<
  E extends EntityKeys,
  A extends ActionKeys<E>
>({
  entityName,
  actionName,
  meta,
  steps,
}: CreateFormClientProps<E, A>) {
  // Valores por defecto para campos requeridos
  const defaultValues = {
    domicilio: {
      fechaDesde: new Date().toISOString().split('T')[0], // Fecha actual
      activo: true, // Por defecto activo
    },
    matricula: {
      fechaAlta: new Date().toISOString().split('T')[0], // Fecha actual
    },
    habilitaciones:[]
  } as any;

  return (
    <Box className="mt-6 md:mt-10 lg:mt-12 md:mx-2 lg:mx-0">
      <ClientFormWrapper
        entityName={entityName}
        actionName={actionName}
        meta={meta}
        steps={steps}
        defaultValues={defaultValues}
        header={(
          <>
          <Stack direction="row" justifyContent="space-between" alignItems={"center"} className="mb-1">
            <Typography
              color="primary.main"
              sx={{ typography: { xs: "h5", md: "h5", lg: "h5xl" } }}
            >
              Nuevo Socio
            </Typography>
            <FormValidateAndSubmitButton label="Guardar" />
          </Stack>
          </>
        )}
      >
      </ClientFormWrapper>
    </Box>
  );
}
